import 'package:flutter/material.dart';
import 'screens/template_list_screen.dart';
import 'services/template_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize the template service
  await TemplateService.instance.initialize();

  runApp(TemplateApp());
}

class TemplateApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Template Manager',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: TemplateListScreen(),
    );
  }
}
