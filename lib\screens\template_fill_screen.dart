import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/template_model.dart';

class TemplateFillScreen extends StatefulWidget {
  final TemplateModel template;

  TemplateFillScreen({required this.template});

  @override
  _TemplateFillScreenState createState() => _TemplateFillScreenState();
}

class _TemplateFillScreenState extends State<TemplateFillScreen> {
  final Map<String, TextEditingController> _controllers = {};
  late String _outputTemplate;

  @override
  void initState() {
    super.initState();
    final placeholders = _extractPlaceholders(widget.template.content);
    for (var placeholder in placeholders) {
      _controllers[placeholder] = TextEditingController();
    }
    _outputTemplate = widget.template.content;
  }

  List<String> _extractPlaceholders(String content) {
    final regex = RegExp(r'\{(.*?)\}');
    return regex.allMatches(content).map((match) => match.group(1)!).toSet().toList();
  }

  void _updateTemplate() {
    String updated = widget.template.content;
    _controllers.forEach((key, controller) {
      updated = updated.replaceAll('{$key}', controller.text);
    });
    setState(() {
      _outputTemplate = updated;
    });
  }

  void _copyToClipboard() {
    Clipboard.setData(ClipboardData(text: _outputTemplate));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Template copied to clipboard!')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.template.title)),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Expanded(
              child: ListView(
                children: [
                  ..._controllers.entries.map((entry) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 10),
                      child: TextField(
                        controller: entry.value,
                        decoration: InputDecoration(
                          labelText: entry.key,
                          border: OutlineInputBorder(),
                        ),
                        onChanged: (_) => _updateTemplate(),
                      ),
                    );
                  }).toList(),
                  SizedBox(height: 20),
                  Text(
                    "Generated Template:",
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  SizedBox(height: 10),
                  Container(
                    padding: EdgeInsets.all(12),
                    color: Colors.grey[200],
                    child: SelectableText(_outputTemplate),
                  ),
                  SizedBox(height: 10),
                  ElevatedButton.icon(
                    onPressed: _copyToClipboard,
                    icon: Icon(Icons.copy),
                    label: Text("Copy Template"),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
