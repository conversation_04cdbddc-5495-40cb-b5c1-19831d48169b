import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/template_model.dart';
import '../services/template_service.dart';

class TemplateFillScreen extends StatefulWidget {
  final TemplateModel template;

  TemplateFillScreen({required this.template});

  @override
  _TemplateFillScreenState createState() => _TemplateFillScreenState();
}

class _TemplateFillScreenState extends State<TemplateFillScreen> {
  final Map<String, TextEditingController> _controllers = {};
  final TemplateService _templateService = TemplateService.instance;
  late String _outputContent;
  String? _outputSubject;

  @override
  void initState() {
    super.initState();
    final placeholders = _templateService.extractAllPlaceholders(widget.template);
    for (var placeholder in placeholders) {
      _controllers[placeholder] = TextEditingController();
    }
    _outputContent = widget.template.content;
    _outputSubject = widget.template.subject;
  }

  @override
  void dispose() {
    _controllers.values.forEach((controller) => controller.dispose());
    super.dispose();
  }

  void _updateTemplate() {
    final values = <String, String>{};
    _controllers.forEach((key, controller) {
      values[key] = controller.text;
    });

    final result = _templateService.fillCompleteTemplate(widget.template, values);

    setState(() {
      _outputContent = result['content'] ?? widget.template.content;
      _outputSubject = result['subject'];
    });
  }

  void _copyToClipboard(String text, String type) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('$type copied to clipboard!')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.template.title)),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Expanded(
              child: ListView(
                children: [
                  // Input fields section
                  if (_controllers.isNotEmpty) ...[
                    Text(
                      "Fill in the placeholders:",
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                    SizedBox(height: 10),
                    ..._controllers.entries.map((entry) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: TextField(
                          controller: entry.value,
                          decoration: InputDecoration(
                            labelText: entry.key,
                            border: OutlineInputBorder(),
                          ),
                          onChanged: (_) => _updateTemplate(),
                        ),
                      );
                    }).toList(),
                    SizedBox(height: 20),
                  ],

                  // Subject section (if exists)
                  if (_outputSubject != null && _outputSubject!.isNotEmpty) ...[
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            "Subject:",
                            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: () => _copyToClipboard(_outputSubject!, 'Subject'),
                          icon: Icon(Icons.copy, size: 16),
                          label: Text("Copy Subject"),
                          style: ElevatedButton.styleFrom(
                            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 10),
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        border: Border.all(color: Colors.blue.shade200),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: SelectableText(
                        _outputSubject!,
                        style: TextStyle(fontSize: 14),
                      ),
                    ),
                    SizedBox(height: 20),
                  ],

                  // Body section
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          "Body:",
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed: () => _copyToClipboard(_outputContent, 'Body'),
                        icon: Icon(Icons.copy, size: 16),
                        label: Text("Copy Body"),
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10),
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: SelectableText(
                      _outputContent,
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                  SizedBox(height: 20),

                  // Copy all button
                  if (_outputSubject != null && _outputSubject!.isNotEmpty)
                    ElevatedButton.icon(
                      onPressed: () {
                        final fullTemplate = 'Subject: $_outputSubject\n\n$_outputContent';
                        _copyToClipboard(fullTemplate, 'Complete template');
                      },
                      icon: Icon(Icons.copy_all),
                      label: Text("Copy Complete Template"),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: 12),
                      ),
                    )
                  else
                    ElevatedButton.icon(
                      onPressed: () => _copyToClipboard(_outputContent, 'Template'),
                      icon: Icon(Icons.copy),
                      label: Text("Copy Template"),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
