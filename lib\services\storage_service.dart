import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/template_model.dart';

class StorageService {
  static const String _templatesKey = 'templates';
  static const String _defaultTemplateKey = 'default_template_id';

  static StorageService? _instance;
  static StorageService get instance => _instance ??= StorageService._();
  StorageService._();

  SharedPreferences? _prefs;

  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  // Template management
  Future<List<TemplateModel>> getTemplates() async {
    await init();
    final String? templatesJson = _prefs?.getString(_templatesKey);
    if (templatesJson == null) {
      return _getDefaultTemplates();
    }

    try {
      final List<dynamic> templatesList = json.decode(templatesJson);
      return templatesList.map((json) => TemplateModel.fromJson(json)).toList();
    } catch (e) {
      return _getDefaultTemplates();
    }
  }

  Future<void> saveTemplates(List<TemplateModel> templates) async {
    await init();
    final String templatesJson = json.encode(templates.map((t) => t.toJson()).toList());
    await _prefs?.setString(_templatesKey, templatesJson);
  }

  Future<void> addTemplate(TemplateModel template) async {
    final templates = await getTemplates();
    templates.add(template);
    await saveTemplates(templates);
  }

  Future<void> updateTemplate(TemplateModel template) async {
    final templates = await getTemplates();
    final index = templates.indexWhere((t) => t.id == template.id);
    if (index != -1) {
      templates[index] = template;
      await saveTemplates(templates);
    }
  }

  Future<void> deleteTemplate(String templateId) async {
    final templates = await getTemplates();
    templates.removeWhere((t) => t.id == templateId);
    await saveTemplates(templates);

    // Clear default template if it was deleted
    final defaultId = await getDefaultTemplateId();
    if (defaultId == templateId) {
      await clearDefaultTemplate();
    }
  }

  // Default template management
  Future<String?> getDefaultTemplateId() async {
    await init();
    return _prefs?.getString(_defaultTemplateKey);
  }

  Future<void> setDefaultTemplate(String templateId) async {
    await init();
    await _prefs?.setString(_defaultTemplateKey, templateId);
  }

  Future<void> clearDefaultTemplate() async {
    await init();
    await _prefs?.remove(_defaultTemplateKey);
  }

  Future<TemplateModel?> getDefaultTemplate() async {
    final defaultId = await getDefaultTemplateId();
    if (defaultId == null) return null;

    final templates = await getTemplates();
    try {
      return templates.firstWhere((t) => t.id == defaultId);
    } catch (e) {
      return null;
    }
  }

  // Default templates for first-time users
  List<TemplateModel> _getDefaultTemplates() {
    return [
      TemplateModel(
        id: '1',
        category: 'LinkedIn',
        title: 'LinkedIn Referral',
        content: "Hey {name}, I'm super interested in the {role} role at {company}—here's the link: {job_url} \n\nI'm a Software Engineer with hands on experience of 4 years, and I think I'd be a great fit. Would you be open to referring me?\n\nHere is my resume: {resume_url}",
      ),
      TemplateModel(
        id: '2',
        category: 'Email',
        title: 'Cold Email',
        subject: 'Interested in {role} Role at {company}',
        content: '''Hey {name},

I am Sachin Pareek and I'm chasing a {role} Role at {company} ({job_url}) that's calling my name. Got 4 years in the game—resume's attached.

You're at {company}, right? Any chance you'd refer me? Takes 2 minutes, and I'd owe you a virtual coffee.

Thanks for even reading this!

Cheers,
Sachin Pareek
<EMAIL> | +91 9660382188''',
      ),
    ];
  }

  // Set the Cold Email template as default for first-time users
  Future<void> initializeDefaultTemplates() async {
    final templates = await getTemplates();
    if (templates.isEmpty) {
      await saveTemplates(_getDefaultTemplates());
      // Set the Cold Email template as default
      await setDefaultTemplate('2');
    }
  }
}
