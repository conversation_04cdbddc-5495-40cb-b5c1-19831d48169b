class TemplateModel {
  String id;
  String category;
  String title;
  String content;
  String? subject; // Optional subject field for email templates

  TemplateModel({
    required this.id,
    required this.category,
    required this.title,
    required this.content,
    this.subject,
  });

  // Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category': category,
      'title': title,
      'content': content,
      'subject': subject,
    };
  }

  // Create from JSON
  factory TemplateModel.fromJson(Map<String, dynamic> json) {
    return TemplateModel(
      id: json['id'],
      category: json['category'],
      title: json['title'],
      content: json['content'],
      subject: json['subject'],
    );
  }

  // Create a copy with updated fields
  TemplateModel copyWith({
    String? id,
    String? category,
    String? title,
    String? content,
    String? subject,
  }) {
    return TemplateModel(
      id: id ?? this.id,
      category: category ?? this.category,
      title: title ?? this.title,
      content: content ?? this.content,
      subject: subject ?? this.subject,
    );
  }
}
