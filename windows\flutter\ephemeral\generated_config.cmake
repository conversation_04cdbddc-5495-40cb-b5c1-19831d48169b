# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Android\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\Projects\\templator" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Android\\flutter"
  "PROJECT_DIR=D:\\Projects\\templator"
  "FLUTTER_ROOT=C:\\Android\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\Projects\\templator\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\Projects\\templator"
  "FLUTTER_TARGET=D:\\Projects\\templator\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\Projects\\templator\\.dart_tool\\package_config.json"
)
