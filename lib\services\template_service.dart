import '../models/template_model.dart';
import 'storage_service.dart';

class TemplateService {
  static TemplateService? _instance;
  static TemplateService get instance => _instance ??= TemplateService._();
  TemplateService._();

  final StorageService _storage = StorageService.instance;

  // Get all templates
  Future<List<TemplateModel>> getAllTemplates() async {
    return await _storage.getTemplates();
  }

  // Add a new template
  Future<void> addTemplate(TemplateModel template) async {
    await _storage.addTemplate(template);
  }

  // Update an existing template
  Future<void> updateTemplate(TemplateModel template) async {
    await _storage.updateTemplate(template);
  }

  // Delete a template
  Future<void> deleteTemplate(String templateId) async {
    await _storage.deleteTemplate(templateId);
  }

  // Get template by ID
  Future<TemplateModel?> getTemplateById(String id) async {
    final templates = await getAllTemplates();
    try {
      return templates.firstWhere((t) => t.id == id);
    } catch (e) {
      return null;
    }
  }

  // Default template management
  Future<String?> getDefaultTemplateId() async {
    return await _storage.getDefaultTemplateId();
  }

  Future<void> setDefaultTemplate(String templateId) async {
    await _storage.setDefaultTemplate(templateId);
  }

  Future<void> clearDefaultTemplate() async {
    await _storage.clearDefaultTemplate();
  }

  Future<TemplateModel?> getDefaultTemplate() async {
    return await _storage.getDefaultTemplate();
  }

  // Generate unique ID for new templates
  String generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  // Extract placeholders from template content
  List<String> extractPlaceholders(String content) {
    final regex = RegExp(r'\{(.*?)\}');
    return regex.allMatches(content).map((match) => match.group(1)!).toSet().toList();
  }

  // Extract placeholders from both subject and content
  List<String> extractAllPlaceholders(TemplateModel template) {
    final Set<String> placeholders = {};
    
    // Extract from content
    placeholders.addAll(extractPlaceholders(template.content));
    
    // Extract from subject if it exists
    if (template.subject != null && template.subject!.isNotEmpty) {
      placeholders.addAll(extractPlaceholders(template.subject!));
    }
    
    return placeholders.toList();
  }

  // Fill template with provided values
  String fillTemplate(String template, Map<String, String> values) {
    String result = template;
    values.forEach((key, value) {
      result = result.replaceAll('{$key}', value);
    });
    return result;
  }

  // Fill both subject and content
  Map<String, String> fillCompleteTemplate(TemplateModel template, Map<String, String> values) {
    final result = <String, String>{};
    
    result['content'] = fillTemplate(template.content, values);
    
    if (template.subject != null && template.subject!.isNotEmpty) {
      result['subject'] = fillTemplate(template.subject!, values);
    }
    
    return result;
  }

  // Initialize service
  Future<void> initialize() async {
    await _storage.init();
    await _storage.initializeDefaultTemplates();
  }
}
