import 'package:flutter/material.dart';
import '../models/template_model.dart';
import '../services/template_service.dart';
import 'template_fill_screen.dart';
import 'template_edit_screen.dart';

class TemplateListScreen extends StatefulWidget {
  @override
  _TemplateListScreenState createState() => _TemplateListScreenState();
}

class _TemplateListScreenState extends State<TemplateListScreen> {
  final TemplateService _templateService = TemplateService.instance;
  List<TemplateModel> templates = [];
  String? defaultTemplateId;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    await _templateService.initialize();
    await _loadTemplates();
  }

  Future<void> _loadTemplates() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final loadedTemplates = await _templateService.getAllTemplates();
      final defaultId = await _templateService.getDefaultTemplateId();

      setState(() {
        templates = loadedTemplates;
        defaultTemplateId = defaultId;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading templates: $e')),
        );
      }
    }
  }

  Future<void> _addTemplate() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => const TemplateEditScreen(isEditing: false),
      ),
    );

    if (result == true) {
      await _loadTemplates();
    }
  }

  Future<void> _editTemplate(TemplateModel template) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => TemplateEditScreen(
          template: template,
          isEditing: true,
        ),
      ),
    );

    if (result == true) {
      await _loadTemplates();
    }
  }

  Future<void> _deleteTemplate(TemplateModel template) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Template'),
        content: Text('Are you sure you want to delete "${template.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _templateService.deleteTemplate(template.id);
        await _loadTemplates();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Template deleted successfully')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting template: $e')),
          );
        }
      }
    }
  }

  Future<void> _setDefaultTemplate(TemplateModel template) async {
    try {
      if (defaultTemplateId == template.id) {
        await _templateService.clearDefaultTemplate();
      } else {
        await _templateService.setDefaultTemplate(template.id);
      }
      await _loadTemplates();

      if (mounted) {
        final message = defaultTemplateId == template.id
            ? 'Default template cleared'
            : '"${template.title}" set as default template';
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(message)),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating default template: $e')),
        );
      }
    }
  }

  Future<void> _useDefaultTemplate() async {
    final defaultTemplate = await _templateService.getDefaultTemplate();
    if (defaultTemplate != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) => TemplateFillScreen(template: defaultTemplate),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No default template set')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Templates"),
        actions: [
          if (defaultTemplateId != null)
            IconButton(
              icon: const Icon(Icons.star),
              onPressed: _useDefaultTemplate,
              tooltip: 'Use Default Template',
            ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addTemplate,
            tooltip: 'Add Template',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : templates.isEmpty
              ? const Center(
                  child: Text(
                    'No templates found.\nTap + to add your first template.',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                )
              : ListView.builder(
                  itemCount: templates.length + (defaultTemplateId != null ? 1 : 0),
                  itemBuilder: (context, index) {
                    // Show default template quick access card first
                    if (defaultTemplateId != null && index == 0) {
                      final defaultTemplate = templates.firstWhere((t) => t.id == defaultTemplateId);
                      return Card(
                        margin: const EdgeInsets.all(8),
                        color: Colors.amber.shade50,
                        child: ListTile(
                          leading: const Icon(Icons.star, color: Colors.amber, size: 32),
                          title: Text(
                            'Quick Access: ${defaultTemplate.title}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          subtitle: Text('Default Template • ${defaultTemplate.category}'),
                          trailing: const Icon(Icons.arrow_forward_ios),
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (_) => TemplateFillScreen(template: defaultTemplate),
                              ),
                            );
                          },
                        ),
                      );
                    }

                    // Adjust index for regular templates
                    final templateIndex = defaultTemplateId != null ? index - 1 : index;
                    final template = templates[templateIndex];
                    final isDefault = defaultTemplateId == template.id;

                    return Card(
                      margin: const EdgeInsets.all(8),
                      child: ListTile(
                        leading: isDefault
                            ? const Icon(Icons.star, color: Colors.amber)
                            : const Icon(Icons.description),
                        title: Text(template.title),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(template.category),
                            if (isDefault)
                              const Text(
                                'Default Template',
                                style: TextStyle(
                                  color: Colors.amber,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                          ],
                        ),
                        trailing: PopupMenuButton<String>(
                          onSelected: (value) {
                            switch (value) {
                              case 'use':
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (_) => TemplateFillScreen(template: template),
                                  ),
                                );
                                break;
                              case 'edit':
                                _editTemplate(template);
                                break;
                              case 'delete':
                                _deleteTemplate(template);
                                break;
                              case 'default':
                                _setDefaultTemplate(template);
                                break;
                            }
                          },
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'use',
                              child: ListTile(
                                leading: Icon(Icons.play_arrow),
                                title: Text('Use Template'),
                                contentPadding: EdgeInsets.zero,
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'edit',
                              child: ListTile(
                                leading: Icon(Icons.edit),
                                title: Text('Edit'),
                                contentPadding: EdgeInsets.zero,
                              ),
                            ),
                            PopupMenuItem(
                              value: 'default',
                              child: ListTile(
                                leading: Icon(
                                  isDefault ? Icons.star_border : Icons.star,
                                  color: isDefault ? null : Colors.amber,
                                ),
                                title: Text(isDefault ? 'Remove Default' : 'Set as Default'),
                                contentPadding: EdgeInsets.zero,
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'delete',
                              child: ListTile(
                                leading: Icon(Icons.delete, color: Colors.red),
                                title: Text('Delete', style: TextStyle(color: Colors.red)),
                                contentPadding: EdgeInsets.zero,
                              ),
                            ),
                          ],
                        ),
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (_) => TemplateFillScreen(template: template),
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addTemplate,
        child: const Icon(Icons.add),
        tooltip: 'Add Template',
      ),
    );
  }
}
