import 'package:flutter/material.dart';
import '../models/template_model.dart';
import 'template_fill_screen.dart';

class TemplateListScreen extends StatelessWidget {
  final List<TemplateModel> templates = [
    TemplateModel(
      id: '1',
      category: 'LinkedIn',
      title: 'LinkedIn Referral',
      content:
          "Hey {name}, I'm super interested in the {role} role at {company}—here's the link: {job_url} \n\nI'm a Software Engineer with hands on experience of 4 years, and I think I'd be a great fit. Would you be open to referring me?\n\nHere is my resume: {resume_url}",
    ),
    TemplateModel(
  id: '2',
  category: 'Email',
  title: 'Cold Email',
  content: '''
Hey {name},

I am Sachin Pareek and I'm chasing a {role} Role at {company} ({job_url}) that's calling my name. Got 4 years in the game—resume's attached.

You're at {company}, right? Any chance you'd refer me? Takes 2 minutes, and I'd owe you a virtual coffee.

Thanks for even reading this!

Cheers,
Sachin Pareek
<EMAIL> | +91 9660382188
''',
),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("Templates")),
      body: ListView.builder(
        itemCount: templates.length,
        itemBuilder: (context, index) {
          final template = templates[index];
          return Card(
            margin: EdgeInsets.all(8),
            child: ListTile(
              title: Text(template.title),
              subtitle: Text(template.category),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (_) => TemplateFillScreen(template: template),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
