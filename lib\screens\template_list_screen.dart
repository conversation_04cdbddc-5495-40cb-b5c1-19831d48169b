import 'package:flutter/material.dart';
import '../models/template_model.dart';
import 'template_fill_screen.dart';
import 'template_edit_screen.dart';

class TemplateListScreen extends StatefulWidget {
  @override
  _TemplateListScreenState createState() => _TemplateListScreenState();
}

class _TemplateListScreenState extends State<TemplateListScreen> {
  List<TemplateModel> templates = [];
  String? defaultTemplateId = '2'; // Set Cold Email as default
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadTemplates();
  }

  void _loadTemplates() {
    setState(() {
      templates = [
        TemplateModel(
          id: '1',
          category: 'LinkedIn',
          title: 'LinkedIn Referral',
          content: "Hey {name}, I'm super interested in the {role} role at {company}—here's the link: {job_url} \n\nI'm a Software Engineer with hands on experience of 4 years, and I think I'd be a great fit. Would you be open to referring me?\n\nHere is my resume: {resume_url}",
        ),
        TemplateModel(
          id: '2',
          category: 'Email',
          title: 'Cold Email',
          subject: 'Interested in {role} Role at {company}',
          content: '''Hey {name},

I am Sachin Pareek and I'm chasing a {role} Role at {company} ({job_url}) that's calling my name. Got 4 years in the game—resume's attached.

You're at {company}, right? Any chance you'd refer me? Takes 2 minutes, and I'd owe you a virtual coffee.

Thanks for even reading this!

Cheers,
Sachin Pareek
<EMAIL> | +91 9660382188''',
        ),
      ];
    });
  }

  void _addTemplate() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => const TemplateEditScreen(isEditing: false),
      ),
    );

    if (result != null && result is TemplateModel) {
      setState(() {
        templates.add(result);
      });
    }
  }

  void _editTemplate(TemplateModel template) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => TemplateEditScreen(
          template: template,
          isEditing: true,
        ),
      ),
    );

    if (result != null && result is TemplateModel) {
      setState(() {
        final index = templates.indexWhere((t) => t.id == template.id);
        if (index != -1) {
          templates[index] = result;
        }
      });
    }
  }

  void _deleteTemplate(TemplateModel template) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Template'),
        content: Text('Are you sure you want to delete "${template.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        templates.removeWhere((t) => t.id == template.id);
        if (defaultTemplateId == template.id) {
          defaultTemplateId = null;
        }
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Template deleted successfully')),
        );
      }
    }
  }

  void _setDefaultTemplate(TemplateModel template) {
    setState(() {
      if (defaultTemplateId == template.id) {
        defaultTemplateId = null;
      } else {
        defaultTemplateId = template.id;
      }
    });

    if (mounted) {
      final message = defaultTemplateId == template.id
          ? '"${template.title}" set as default template'
          : 'Default template cleared';
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message)),
      );
    }
  }

  void _useDefaultTemplate() {
    if (defaultTemplateId != null) {
      final defaultTemplate = templates.firstWhere((t) => t.id == defaultTemplateId);
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) => TemplateFillScreen(template: defaultTemplate),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No default template set')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Templates"),
        actions: [
          if (defaultTemplateId != null)
            IconButton(
              icon: const Icon(Icons.star),
              onPressed: _useDefaultTemplate,
              tooltip: 'Use Default Template',
            ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addTemplate,
            tooltip: 'Add Template',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : templates.isEmpty
              ? const Center(
                  child: Text(
                    'No templates found.\nTap + to add your first template.',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                )
              : ListView.builder(
                  itemCount: templates.length + (defaultTemplateId != null ? 1 : 0),
                  itemBuilder: (context, index) {
                    // Show default template quick access card first
                    if (defaultTemplateId != null && index == 0) {
                      final defaultTemplate = templates.firstWhere((t) => t.id == defaultTemplateId);
                      return Card(
                        margin: const EdgeInsets.all(8),
                        color: Colors.amber.shade50,
                        child: ListTile(
                          leading: const Icon(Icons.star, color: Colors.amber, size: 32),
                          title: Text(
                            'Quick Access: ${defaultTemplate.title}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          subtitle: Text('Default Template • ${defaultTemplate.category}'),
                          trailing: const Icon(Icons.arrow_forward_ios),
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (_) => TemplateFillScreen(template: defaultTemplate),
                              ),
                            );
                          },
                        ),
                      );
                    }

                    // Adjust index for regular templates
                    final templateIndex = defaultTemplateId != null ? index - 1 : index;
                    final template = templates[templateIndex];
                    final isDefault = defaultTemplateId == template.id;

                    return Card(
                      margin: const EdgeInsets.all(8),
                      child: ListTile(
                        leading: isDefault
                            ? const Icon(Icons.star, color: Colors.amber)
                            : const Icon(Icons.description),
                        title: Text(template.title),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(template.category),
                            if (isDefault)
                              const Text(
                                'Default Template',
                                style: TextStyle(
                                  color: Colors.amber,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                          ],
                        ),
                        trailing: PopupMenuButton<String>(
                          onSelected: (value) {
                            switch (value) {
                              case 'use':
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (_) => TemplateFillScreen(template: template),
                                  ),
                                );
                                break;
                              case 'edit':
                                _editTemplate(template);
                                break;
                              case 'delete':
                                _deleteTemplate(template);
                                break;
                              case 'default':
                                _setDefaultTemplate(template);
                                break;
                            }
                          },
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'use',
                              child: ListTile(
                                leading: Icon(Icons.play_arrow),
                                title: Text('Use Template'),
                                contentPadding: EdgeInsets.zero,
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'edit',
                              child: ListTile(
                                leading: Icon(Icons.edit),
                                title: Text('Edit'),
                                contentPadding: EdgeInsets.zero,
                              ),
                            ),
                            PopupMenuItem(
                              value: 'default',
                              child: ListTile(
                                leading: Icon(
                                  isDefault ? Icons.star_border : Icons.star,
                                  color: isDefault ? null : Colors.amber,
                                ),
                                title: Text(isDefault ? 'Remove Default' : 'Set as Default'),
                                contentPadding: EdgeInsets.zero,
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'delete',
                              child: ListTile(
                                leading: Icon(Icons.delete, color: Colors.red),
                                title: Text('Delete', style: TextStyle(color: Colors.red)),
                                contentPadding: EdgeInsets.zero,
                              ),
                            ),
                          ],
                        ),
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (_) => TemplateFillScreen(template: template),
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addTemplate,
        child: const Icon(Icons.add),
        tooltip: 'Add Template',
      ),
    );
  }
}
